<?php

declare(strict_types=1);

use App\Http\Controllers\Teacher\AttendanceController;
use App\Http\Controllers\Teacher\ClassroomController;
use App\Http\Controllers\Teacher\QuestionsController;
use App\Http\Controllers\Teacher\TodoController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth:teacher', 'verified'])->group(function () {
    Route::get('/', function () {
        return Inertia::render('welcome');
    })->name('home');

    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('classrooms', [ClassroomController::class, 'index'])->name(
        'classrooms',
    );

    Route::get('classroom/{classroom}/home', [
        ClassroomController::class,
        'show',
    ])->name('classroom');

    Route::get('todos/{classroom}', [TodoController::class, 'index'])->name(
        'todos',
    );

    Route::post('todos', [TodoController::class, 'store'])->name('todo.store');

    Route::post('todos/{todo}/done', [TodoController::class, 'done'])->name(
        'todo.done',
    );

    Route::post('todos/{todo}/undone', [
        TodoController::class,
        'rollback',
    ])->name('todo.undone');

    Route::delete('classroom/todo/{todo}', [
        TodoController::class,
        'destroy',
    ])->name('todo.destroy');

    Route::post('todos/{id}/restore', [TodoController::class, 'restore'])->name(
        'todo.restore',
    );

    Route::get('classroom/{classroom}/attendance', [
        AttendanceController::class,
        'index',
    ])->name('attendance');

    Route::post('classroom/{classroom}/attendance', [
        AttendanceController::class,
        'store',
    ])->name('attendance.store');

    Route::put('classroom/{classroom}/attendance', [
        AttendanceController::class,
        'update',
    ])->name('attendance.update');

    Route::get('classroom/{classroom}/questions', [
        QuestionsController::class,
        'index',
    ])->name('questions');

    Route::get('classroom/{classroom}/questions/create', [
        QuestionsController::class,
        'create',
    ])->name('questions.create');

    Route::post('classroom/{classroom}/questions', [
        QuestionsController::class,
        'store',
    ])->name('questions.store');

    Route::get('classroom/{classroom}/questions/{question}/edit', [
        QuestionsController::class,
        'edit',
    ])->name('questions.edit');

    Route::put('classroom/{classroom}/{question}', [
        QuestionsController::class,
        'update',
    ])->name('questions.update');

    Route::delete('questions/{question}', [
        QuestionsController::class,
        'destroy',
    ])->name('questions.destroy');

    Route::get('test', function () {
        return Inertia::render('test');
    })->name('test');

    Route::get('api/chapters', function () {
        return App\Models\Chapter::all()->sortBy('number');
    });

    Route::get('api/eighths', function () {
        return App\Models\Verse::with('chapter')->where('eighth', true)->get();
    });

    Route::get('verses', function () {
        $query = App\Models\Verse::with('chapter');

        if (request('search')) {
            $query->whereTextStartsWith(request('search'));
        }

        if (request('selectedChapter')) {
            $query->where('chapter_id', request('selectedChapter'));
        }

        $page = request('page', 1);
        $verses = $query
            ->join('chapters', 'verses.chapter_id', '=', 'chapters.id')
            ->orderBy('chapters.number')
            ->orderBy('verses.number')
            ->select('verses.*')
            ->forPage($page, 30)
            ->get();

        return $verses;
    });
});

Route::get('/quran', function () {
    return view('Quran');
});

require __DIR__.'/auth.php';
