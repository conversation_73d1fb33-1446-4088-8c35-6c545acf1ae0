{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "bezhansalleh/filament-shield": "^4.0", "filament/filament": "^4.0", "filament/forms": "^4.0", "filament/spatie-laravel-media-library-plugin": "^4.0", "filament/tables": "^4.0", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^12.0", "laravel/tinker": "^2.10.1", "maatwebsite/excel": "^3.1.64", "saade/blade-iconsax": "^1.2", "spatie/laravel-activitylog": "^4.10.1", "spatie/laravel-permission": "^6.18.0", "tightenco/ziggy": "^2.5"}, "require-dev": {"fakerphp/faker": "^1.23", "filament/upgrade": "^4.0", "larastan/larastan": "^3.6", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.8", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2", "pestphp/pest-plugin-livewire": "^3.0", "pestphp/pest-plugin-type-coverage": "^3.5.1", "phpunit/phpunit": "^11.5.3", "rector/rector": "^2.0.16"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"npm run dev\" --names='server,queue,vite'"], "lint": "pint", "refactor": "rector", "phpstan": "vendor/bin/phpstan -c phpstan.neon", "test:lint": "pint --test", "test:refactor": "rector --dry-run", "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "beta", "prefer-stable": true}