<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\StudentResource\Pages\CreateStudent;
use App\Filament\Resources\StudentResource\Pages\EditStudent;
use App\Filament\Resources\StudentResource\Pages\ListStudents;
use App\Filament\Resources\StudentResource\Pages\ManageExams;
use App\Filament\Resources\StudentResource\Pages\ManageProgression;
use App\Models\Classroom;
use App\Models\Guardian;
use App\Models\Student;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use UnitEnum;

class StudentResource extends Resource
{
    protected static ?string $model = Student::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-academic-cap';

    protected static string|UnitEnum|null $navigationGroup = 'School Management';

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    protected static ?int $navigationSort = 2;

    public static function getPluralModelLabel(): string
    {
        return __('Students');
    }

    public static function getLabel(): string
    {
        return __('Student');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(static::$navigationGroup);
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Group::make([])
                    ->schema([
                        Section::make(__('Student Information'))
                            ->schema([
                                TextInput::make('name')
                                    ->label('Full Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder(
                                        __('Enter student full name'),
                                    ),

                                TextInput::make('age')
                                    ->label('Age')
                                    ->translateLabel()
                                    ->numeric()
                                    ->required(),

                                DatePicker::make('date_of_birth')
                                    ->label('Date of Birth')
                                    ->translateLabel()
                                    ->required()
                                    ->maxDate(now())
                                    ->displayFormat('Y-m-d'),

                                Select::make('gender')
                                    ->label('Gender')
                                    ->translateLabel()
                                    ->options([
                                        'male' => __('Male'),
                                        'female' => __('Female'),
                                    ])
                                    ->required(),

                                TextInput::make('grade_level')
                                    ->label('Grade Level')
                                    ->translateLabel()
                                    ->maxLength(255)
                                    ->placeholder(
                                        __('e.g., Grade 5, High School'),
                                    ),

                                Select::make('classroom_id')
                                    ->label('Classroom')
                                    ->translateLabel()
                                    ->options(
                                        Classroom::all()->pluck('name', 'id'),
                                    )
                                    ->disabled()
                                    ->searchable(),

                                TextInput::make('registration_number')
                                    ->label('Registration Number')
                                    ->translateLabel()
                                    ->unique(
                                        Student::class,
                                        'registration_number',
                                        ignoreRecord: true,
                                    )
                                    ->columnSpan(2)
                                    ->disabled(),
                            ])
                            ->columns(2),

                        Section::make(__('Additional Information'))->schema([
                            Textarea::make('medical_conditions')
                                ->label('Medical Conditions')
                                ->translateLabel()
                                ->rows(3)
                                ->placeholder(
                                    __('Any medical conditions or allergies'),
                                ),

                            Textarea::make('notes')
                                ->label('Notes')
                                ->translateLabel()
                                ->rows(3)
                                ->placeholder(
                                    __('Additional notes about the student'),
                                ),
                        ]),
                    ])
                    ->columnSpan(2),
                Group::make([
                    Section::make(__('Student Profile & Media'))
                        ->description(
                            __(
                                'Manage student status, guardian details, and upload personal documents and photos.',
                            ),
                        )
                        ->schema([
                            Select::make('is_active')
                                ->label('Active Student')
                                ->translateLabel()
                                ->options([
                                    1 => __('Active'),
                                    0 => __('Inactive'),
                                ])
                                ->default('inactive'),
                            Select::make('guardian_id')
                                ->label('Guardian')
                                ->translateLabel()
                                ->options(Guardian::all()->pluck('name', 'id'))
                                ->searchable()
                                ->required()
                                ->createOptionForm([
                                    TextInput::make('name')
                                        ->label('Full Name')
                                        ->translateLabel()
                                        ->required()
                                        ->maxLength(255),
                                    TextInput::make('email')
                                        ->label('Email Address')
                                        ->translateLabel()
                                        ->email()
                                        ->required()
                                        ->unique(Guardian::class),
                                    TextInput::make('phone')
                                        ->label('Phone Number')
                                        ->translateLabel()
                                        ->required(),
                                    Textarea::make('address')
                                        ->label('Address')
                                        ->translateLabel()
                                        ->required(),
                                ])
                                ->createOptionUsing(function (
                                    array $data,
                                ): int {
                                    return Guardian::create($data)->getKey();
                                }),
                            SpatieMediaLibraryFileUpload::make('student_photo')
                                ->collection('student_photo')
                                ->label('Personal Photo')
                                ->translateLabel()
                                ->disk('local')
                                ->visibility('private')
                                ->downloadable(),
                            SpatieMediaLibraryFileUpload::make(
                                'student_documents',
                            )
                                ->collection('student_documents')
                                ->label('Documents')
                                ->translateLabel()
                                ->disk('local')
                                ->multiple()
                                ->visibility('private')
                                ->downloadable(),
                        ]),
                ])->columnSpan(1),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('guardian.name')
                    ->label('Guardian')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('classroom.name')
                    ->label('Classroom')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('age')
                    ->label('Age')
                    ->translateLabel()
                    ->sortable(),

                TextColumn::make('date_of_birth')
                    ->label('Date of Birth')
                    ->translateLabel()
                    ->date()
                    ->sortable(),

                TextColumn::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->sortable()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state)),

                TextColumn::make('grade_level')
                    ->label('Grade Level')
                    ->translateLabel()
                    ->sortable()
                    ->searchable(),

                TextColumn::make('registration_number')
                    ->label('Registration Number')
                    ->translateLabel()
                    ->sortable()
                    ->searchable(),

                IconColumn::make('is_active')
                    ->translateLabel()
                    ->boolean()
                    ->label('Active')
                    ->translateLabel(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('guardian')
                    ->label('Guardian')
                    ->translateLabel()
                    ->relationship('guardian', 'name')
                    ->searchable(),

                SelectFilter::make('classroom')
                    ->label('Classroom')
                    ->translateLabel()
                    ->relationship('classroom', 'name')
                    ->searchable(),

                SelectFilter::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->options([
                        'male' => __('Male'),
                        'female' => __('Female'),
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([DeleteBulkAction::make()]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditStudent::class,
            ManageProgression::class,
            ManageExams::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListStudents::route('/'),
            'create' => CreateStudent::route('/create'),
            'edit' => EditStudent::route('/{record}/edit'),
            'progress' => ManageProgression::route('/{record}/progress'),
            'exams' => ManageExams::route('/{record}/exams'),
        ];
    }
}
