<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams\Pages;

use App\Filament\Resources\Exams\ExamResource;
use App\Models\ExamStudentQuestion;
use App\Models\Setting;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;

class ManageResults extends ManageRelatedRecords
{
    protected static string $resource = ExamResource::class;

    protected static string $relationship = 'studentQuestions';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::BookOpen;

    public static function getNavigationLabel(): string
    {
        return __('Manage Results');
    }

    public function getTitle(): string
    {
        return __('Manage Results');
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(
                fn ($query) => $query->with(['student', 'question', 'ratings']),
            )
            ->groups([Group::make('student.name')->label(__('Student'))])
            ->defaultGroup('student.name')
            ->columns([
                TextColumn::make('question_label')
                    ->label('Question')
                    ->translateLabel()
                    ->state(function (ExamStudentQuestion $record) {
                        $question = $record->question;

                        return $question->title;
                    }),
                TextColumn::make('memorization_score')
                    ->label('memorization')
                    ->translateLabel()
                    ->state(
                        fn (
                            ExamStudentQuestion $record,
                        ) => $record->ratings->firstWhere(
                            'aspect',
                            'memorization',
                        )?->score ?? '-',
                    ),

                TextColumn::make('tajweed_score')
                    ->label('tajweed_rules')
                    ->translateLabel()
                    ->state(
                        fn (
                            ExamStudentQuestion $record,
                        ) => $record->ratings->firstWhere(
                            'aspect',
                            'tajweed_rules',
                        )?->score ?? '-',
                    ),

                TextColumn::make('narration_score')
                    ->label('narration_principles')
                    ->translateLabel()
                    ->state(
                        fn (
                            ExamStudentQuestion $record,
                        ) => $record->ratings->firstWhere(
                            'aspect',
                            'narration_principles',
                        )?->score ?? '-',
                    ),

                TextColumn::make('stopping_score')
                    ->label('stopping_starting_rules')
                    ->translateLabel()
                    ->state(
                        fn (
                            ExamStudentQuestion $record,
                        ) => $record->ratings->firstWhere(
                            'aspect',
                            'stopping_starting_rules',
                        )?->score ?? '-',
                    ),

                TextColumn::make('recitation_score')
                    ->label('recitation_performance')
                    ->translateLabel()
                    ->state(
                        fn (
                            ExamStudentQuestion $record,
                        ) => $record->ratings->firstWhere(
                            'aspect',
                            'recitation_performance',
                        )?->score ?? '-',
                    ),

                TextColumn::make('sound_score')
                    ->label('sound')
                    ->translateLabel()
                    ->state(
                        fn (
                            ExamStudentQuestion $record,
                        ) => $record->ratings->firstWhere('aspect', 'sound')
                            ?->score ?? '-',
                    ),
            ])
            ->recordActions([
                Action::make('editResult')
                    ->label('') // Remove label for icon-only
                    ->tooltip(__('Edit Result')) // Optional hover tooltip
                    ->icon('heroicon-o-pencil-square')
                    ->iconButton()
                    ->color('primary')
                    ->schema(function (ExamStudentQuestion $record) {
                        $ratings = $record->ratings->keyBy('aspect');
                        $settings = Setting::all()->keyBy('key');

                        $fields = [];
                        foreach ($settings as $aspect => $config) {
                            $weight = (float) ($config->value['weight'] ?? 0);
                            $deduction =
                                (float) ($config->value[
                                    'deduction_per_mistake'
                                ] ?? 0);

                            $maxMistakesText = __('N/A');
                            $maxMistakesValue = 9999;

                            if ($deduction > 0) {
                                $calculatedMax = floor($weight / $deduction);
                                $maxMistakesText = (string) $calculatedMax;
                                $maxMistakesValue = $calculatedMax;
                            }

                            $fields[] = TextInput::make($aspect)
                                ->label($aspect)
                                ->translateLabel()
                                ->prefix(
                                    __('Maximum Mistakes').
                                        ': '.
                                        $maxMistakesText,
                                )
                                ->suffix(
                                    $deduction > 0 ? (string) $deduction : null,
                                )
                                ->minValue(0)
                                ->maxValue($maxMistakesValue)
                                ->numeric()
                                ->step(1)
                                ->default($ratings[$aspect]?->mistakes ?? 0);
                        }

                        return $fields;
                    })
                    ->action(function (
                        array $data,
                        ExamStudentQuestion $record,
                    ) {
                        foreach ($data as $aspect => $mistakes) {
                            $record
                                ->ratings()
                                ->updateOrCreate(
                                    ['aspect' => $aspect],
                                    ['mistakes' => $mistakes],
                                );
                        }
                    }),
                Action::make('resetQuestion')
                    ->label('')
                    ->tooltip(__('Reset Question'))
                    ->icon('heroicon-o-trash')
                    ->iconButton()
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Reset this Question?')
                    ->modalDescription(
                        'This will delete all ratings for this question. Are you sure?',
                    )
                    ->action(function (ExamStudentQuestion $record) {
                        $record->ratings()->delete();
                    }),
            ]);
    }
}
