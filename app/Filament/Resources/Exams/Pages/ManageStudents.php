<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams\Pages;

use App\Filament\Resources\Exams\ExamResource;
use App\Models\Exam;
use App\Models\ExamStudent;
use App\Models\ExamStudentQuestion;
use App\Models\Question;
use App\Models\Student;
use BackedEnum;
use DB;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Support\Icons\Heroicon;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ManageStudents extends ManageRelatedRecords
{
    use InteractsWithRecord;

    protected static string $resource = ExamResource::class;

    protected static string|null|BackedEnum $navigationIcon = Heroicon::UserGroup;

    protected static string $relationship = 'students'; // refers to Exam::students()

    public static function getNavigationLabel(): string
    {
        return __('Manage Students');
    }

    public function getTitle(): string
    {
        return __('Manage Students');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student.name')
                    ->label('Student Name')
                    ->translateLabel()
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_started')
                    ->label('Started')
                    ->translateLabel()
                    ->boolean(),

                Tables\Columns\TextColumn::make('started_at')
                    ->label('Started At')
                    ->translateLabel()
                    ->dateTime(),

                Tables\Columns\TextColumn::make('completed_at')
                    ->label('Completed At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->recordActions([
                Actions\Action::make('editQuestions')
                    ->label('Edit Questions')
                    ->slideOver()
                    ->schema(function (ExamStudent $record) {
                        return [
                            Repeater::make('questions')
                                ->label('Questions')
                                ->translateLabel()
                                ->orderColumn()
                                ->schema([
                                    Select::make('question_id')
                                        ->label('Question')
                                        ->translateLabel()
                                        ->disableOptionsWhenSelectedInSiblingRepeaterItems() // Only works in v3
                                        ->options(function () {
                                            $teacherId =
                                                $this->record->teacher_id;

                                            return [
                                                __(
                                                    'Global Questions',
                                                ) => $this->formatQuestions(
                                                    Question::with([
                                                        'verse.chapter',
                                                        'startVerse.chapter',
                                                        'endVerse.chapter',
                                                    ])
                                                        ->whereNull(
                                                            'teacher_id',
                                                        )
                                                        ->get(),
                                                ),
                                                __(
                                                    'Teacher Questions',
                                                ) => $this->formatQuestions(
                                                    Question::with([
                                                        'verse.chapter',
                                                        'startVerse.chapter',
                                                        'endVerse.chapter',
                                                    ])
                                                        ->where(
                                                            'teacher_id',
                                                            $teacherId,
                                                        )
                                                        ->get(),
                                                ),
                                            ];
                                        })
                                        ->searchable()
                                        ->preload()
                                        ->required(),
                                ])
                                ->default(
                                    ExamStudentQuestion::where(
                                        'exam_id',
                                        $record->exam_id,
                                    )
                                        ->where(
                                            'student_id',
                                            $record->student_id,
                                        )
                                        ->get()
                                        ->map(
                                            fn ($item) => [
                                                'question_id' => $item->question_id,
                                            ],
                                        )
                                        ->toArray(),
                                )
                                ->addActionLabel(__('Add Question'))
                                ->minItems(1)
                                ->required()
                                ->reorderable(false)
                                ->columns(1),
                        ];
                    })
                    ->action(function (array $data, ExamStudent $record) {
                        // Remove all old questions
                        ExamStudentQuestion::where('exam_id', $record->exam_id)
                            ->where('student_id', $record->student_id)
                            ->delete();

                        // Insert selected questions from repeater
                        foreach ($data['questions'] as $item) {
                            ExamStudentQuestion::create([
                                'exam_id' => $record->exam_id,
                                'student_id' => $record->student_id,
                                'question_id' => $item['question_id'],
                            ]);
                        }
                    }),
            ]);
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return ExamStudent::query()->where('exam_id', $this->record->id);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('addStudent')
                ->label('Add Student')
                ->translateLabel()
                ->slideOver()
                ->schema(function () {
                    return [
                        Select::make('student_id')
                            ->label('Student')
                            ->translateLabel()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                /** @var Exam $exam */
                                $exam = $this->record;

                                return $exam && $exam->classroom
                                    ? $exam->classroom->students
                                        ->whereNotIn(
                                            'id',
                                            $exam->students->pluck(
                                                'student_id',
                                            ),
                                        )
                                        ->pluck('name', 'id')
                                    : [];
                            })
                            ->required(),

                        Repeater::make('questions')
                            ->label('Questions')
                            ->translateLabel()
                            ->schema([
                                Select::make('question_id')
                                    ->label('Question')
                                    ->translateLabel()
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->options(function () {
                                        $teacherId = $this->record->teacher_id;

                                        return [
                                            __(
                                                'Global Questions',
                                            ) => $this->formatQuestions(
                                                Question::with([
                                                    'verse.chapter',
                                                    'startVerse.chapter',
                                                    'endVerse.chapter',
                                                ])
                                                    ->whereNull('teacher_id')
                                                    ->get(),
                                            ),
                                            __(
                                                'Teacher Questions',
                                            ) => $this->formatQuestions(
                                                Question::with([
                                                    'verse.chapter',
                                                    'startVerse.chapter',
                                                    'endVerse.chapter',
                                                ])
                                                    ->where(
                                                        'teacher_id',
                                                        $teacherId,
                                                    )
                                                    ->get(),
                                            ),
                                        ];
                                    })
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                            ])
                            ->addActionLabel(__('Add Question'))
                            ->minItems(1)
                            ->required()
                            ->reorderable(false)
                            ->columns(1),

                        /*Forms\Components\Select::make('question_ids')
                            ->label('Questions')
                            ->translateLabel()
                            ->multiple()
                            ->options(function () {
                                $teacherId = $this->record->teacher_id;

                                return [
                                    __(
                                        'Global Questions',
                                    ) => $this->formatQuestions(
                                        Question::with([
                                            'verse.chapter',
                                            'startVerse.chapter',
                                            'endVerse.chapter',
                                        ])
                                            ->whereNull('teacher_id')
                                            ->get(),
                                    ),

                                    __(
                                        'Teacher Questions',
                                    ) => $this->formatQuestions(
                                        Question::with([
                                            'verse.chapter',
                                            'startVerse.chapter',
                                            'endVerse.chapter',
                                        ])
                                            ->where('teacher_id', $teacherId)
                                            ->get(),
                                    ),
                                ];
                            })
                            ->preload()
                            ->searchable()
                            ->required(),*/
                    ];
                })
                ->action(function (array $data): void {
                    $exam = $this->record;

                    DB::transaction(function () use ($exam, $data) {
                        // 1. Add to exam_student if not exists
                        ExamStudent::firstOrCreate([
                            'exam_id' => $exam->id,
                            'student_id' => $data['student_id'],
                        ]);

                        // 2. Attach each selected question to the student
                        foreach ($data['questions'] as $question) {
                            ExamStudentQuestion::firstOrCreate([
                                'exam_id' => $exam->id,
                                'student_id' => $data['student_id'],
                                'question_id' => $question['question_id'],
                            ]);
                        }
                    });
                }),
        ];
    }

    protected function formatQuestions(Collection $questions): array
    {
        return $questions
            ->mapWithKeys(function (Question $question) {
                if ($question->verse) {
                    $label =
                        'Verse: '.
                        $question->verse->chapter->name.
                        ' - '.
                        $question->verse->number;
                } elseif ($question->startVerse && $question->endVerse) {
                    $label =
                        'Range: '.
                        $question->startVerse->chapter->name.
                        ' - '.
                        $question->startVerse->number.
                        ' to '.
                        $question->endVerse->chapter->name.
                        ' - '.
                        $question->endVerse->number;
                } else {
                    $label =
                        'Unnamed Question ('.
                        Str::limit($question->id, 8).
                        ')';
                }

                return [$question->id => $label];
            })
            ->toArray();
    }
}
