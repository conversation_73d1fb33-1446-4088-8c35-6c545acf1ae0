<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomResource\Pages;

use App\Filament\Resources\ClassroomResource;
use App\Models\AttendanceSchedule;
use App\Models\Classroom;
use App\Models\Student;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ManageAttendance extends ManageRelatedRecords
{
    public string $date;

    public Classroom $classroom;

    protected static string $resource = ClassroomResource::class;

    protected static string $relationship = 'attendanceRecords';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-calendar-days';

    protected string $view = 'filament.resources.classroom-resource.pages.manage-attendance';

    public static function getNavigationLabel(): string
    {
        return __('Attendance');
    }

    public function getRecord(): Model
    {
        return $this->classroom;
    }

    public function mount($record): void
    {
        $this->classroom = Classroom::findOrFail($record);
        $this->date = now()->toDateString();
    }

    public function updatedDate(): void
    {
        $this->table->query(
            AttendanceSchedule::query()
                ->where('classroom_id', $this->classroom->id)
                ->whereDate('date', $this->date)
                ->with('student')
        );
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                AttendanceSchedule::query()
                    ->where('classroom_id', $this->classroom->id)
                    ->whereDate('date', $this->date)
                    ->with('student')
            )
            ->columns([
                TextColumn::make('student.name')
                    ->label('Student')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->colors([
                        'success' => 'Present',
                        'danger' => 'Absent',
                    ]),
                TextColumn::make('date')
                    ->label('Date')
                    ->translateLabel()
                    ->date(),
            ])
            ->recordActions([
                EditAction::make()
                    ->label('Edit')
                    ->translateLabel()
                    ->modalHeading(__('Edit Attendance'))
                    ->modalSubmitActionLabel(__('Save'))
                    ->mountUsing(function (Schema $schema, AttendanceSchedule $record): void {
                        $schema->fill([
                            'type' => $record->type,
                            'notes' => $record->notes,
                        ]);
                    })
                    ->action(function (AttendanceSchedule $record, array $data): void {
                        $record->update([
                            'type' => $data['type'],
                            'operator_id' => auth()->id(),
                            'operator_type' => get_class(auth()->user()),
                            'notes' => $data['notes'] ?? null,
                        ]);

                        Notification::make()
                            ->title(__('Attendance Updated'))
                            ->success()
                            ->send();
                    })
                    ->schema([
                        Select::make('type')
                            ->label('Status')
                            ->translateLabel()
                            ->live()
                            ->options([
                                'Present' => __('Present'),
                                'Absent' => __('Absent'),
                            ])
                            ->required(),
                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->visible(fn (Get $get): bool => $get('type') === 'Absent')
                            ->rows(3)
                            ->placeholder(__('Any additional notes about the attendance')),
                    ]),
                DeleteAction::make(),
            ])
            ->headerActions([
                Action::make('markAttendance')
                    ->label('Mark Attendance')
                    ->translateLabel()
                    ->modalHeading(__('Mark Attendance'))
                    ->modalSubmitActionLabel(__('Mark Attendance'))
                    ->action(function (array $data): void {
                        $student = Student::findOrFail($data['student_id']);

                        AttendanceSchedule::updateOrCreate(
                            [
                                'student_id' => $data['student_id'],
                                'classroom_id' => $this->classroom->id,
                                'date' => $this->date,
                            ],
                            [
                                'type' => $data['type'],
                                'operator_id' => auth()->id(),
                                'operator_type' => get_class(auth()->user()),
                            ]
                        );

                        Notification::make()
                            ->title(__('Attendance Marked'))
                            ->body(__('The attendance has been marked to '.$student->name.' successfully.'))
                            ->success()
                            ->send();
                    })
                    ->schema([
                        Select::make('student_id')
                            ->label('Student')
                            ->translateLabel()
                            ->options(fn () => $this->classroom->students()
                                ->whereDoesntHave('attendanceRecords', fn ($query) => $query->whereDate('date', $this->date))
                                ->pluck('name', 'id'))
                            ->required(),
                        Select::make('type')
                            ->label('Status')
                            ->translateLabel()
                            ->live()
                            ->options([
                                'Present' => __('Present'),
                                'Absent' => __('Absent'),
                            ])
                            ->required(),
                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->visible(fn (Get $get): bool => $get('type') === 'Absent')
                            ->rows(3)
                            ->placeholder(__('Any additional notes about the attendance')),
                    ]),
                Action::make('markAllPresent')
                    ->label('Mark All Present')
                    ->translateLabel()
                    ->color('success')
                    ->requiresConfirmation()
                    ->action(fn () => $this->markAll('Present')),
                Action::make('markAllAbsent')
                    ->label('Mark All Absent')
                    ->translateLabel()
                    ->color('danger')
                    ->schema([
                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Any additional notes about the attendance')),
                    ])
                    ->action(fn ($data) => $this->markAll('Absent', $data['notes'])),
            ])
            ->defaultSort('student.name');
    }

    public function markAll(string $type, ?string $notes = null): void
    {
        $studentsWithoutRecord = $this->classroom->students()
            ->whereDoesntHave('attendanceRecords', fn ($query) => $query->whereDate('date', $this->date))
            ->get();

        $records = $studentsWithoutRecord->map(fn (Student $student) => [
            'id' => (string) Str::uuid(),
            'student_id' => $student->id,
            'classroom_id' => $this->classroom->id,
            'date' => $this->date,
            'type' => $type,
            'notes' => $notes,
            'operator_id' => auth()->id(),
            'operator_type' => get_class(auth()->user()),
            'created_at' => now(),
            'updated_at' => now(),
        ])->all();

        if (! empty($records)) {
            AttendanceSchedule::insert($records);
        }

        Notification::make()
            ->title(__('Attendance Marked'))
            ->body(__('The attendance has been marked successfully.'))
            ->success()
            ->send();
    }

    public function getTitle(): string|Htmlable
    {
        return __('Manage Attendance');
    }
}
