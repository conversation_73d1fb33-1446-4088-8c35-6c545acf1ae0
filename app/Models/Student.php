<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\StudentObserver;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

#[ObservedBy([StudentObserver::class])]
class Student extends Model implements HasMedia
{
    use HasFactory, HasUuids, InteractsWithMedia;

    protected $fillable = [
        'guardian_id',
        'classroom_id',
        'name',
        'registration_number',
        'age',
        'date_of_birth',
        'grade_level',
        'gender',
        'medical_conditions',
        'notes',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the guardian that owns the student
     */
    public function guardian(): BelongsTo
    {
        return $this->belongsTo(Guardian::class);
    }

    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class);
    }

    /**
     * Get all attendance records for this student
     */
    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceSchedule::class);
    }

    /**
     * Get attendance records for a specific date
     */
    public function attendanceForDate($date): ?AttendanceSchedule
    {
        return $this->attendanceRecords()->whereDate('date', $date)->first();
    }

    /**
     * Check if a student is present on a specific date
     */
    public function isPresentOnDate($date): bool
    {
        $attendance = $this->attendanceForDate($date);

        return $attendance && $attendance->isPresent();
    }

    /**
     * Get attendance percentage for a date range
     */
    public function getAttendancePercentage(
        $startDate = null,
        $endDate = null,
    ): float {
        $query = $this->attendanceRecords();

        if ($startDate) {
            $query->whereDate('date', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('date', '<=', $endDate);
        }

        $totalRecords = $query->count();
        $presentRecords = $query->where('type', 'Present')->count();

        return $totalRecords > 0 ? ($presentRecords / $totalRecords) * 100 : 0;
    }

    /**
     * Get the student's full name with guardian info
     */
    public function getFullNameWithGuardianAttribute(): string
    {
        return $this->name.' ('.$this->guardian->name.')';
    }

    /**
     * Calculate age from date of birth
     */
    public function getCalculatedAgeAttribute(): int
    {
        return Carbon::parse($this->date_of_birth)->age;
    }

    /**
     * Scope for active students
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for students by gender
     */
    public function scopeByGender($query, string $gender)
    {
        return $query->where('gender', $gender);
    }

    public function generateRegistrationNumber(): string
    {
        // 1 = male, 2 = female
        $genderDigit =
            strtolower((string) $this->gender) === 'male' ? '1' : '2';

        // Get classroom_number (not classroom_id)
        $classroomNumber = (string) optional($this->classroom)
            ->classroom_number;

        // Ensure classroom_number is set
        if (empty($classroomNumber)) {
            throw new RuntimeException('Classroom number is not assigned.');
        }

        // Format birth day and month
        $birthDay = Carbon::parse($this->date_of_birth)->format('d');
        $birthMonth = Carbon::parse($this->date_of_birth)->format('n'); // one or two digits

        // Build prefix
        $prefix = $genderDigit.$classroomNumber.$birthDay.$birthMonth;

        // Get max existing student_counter for this prefix
        $lastNumber =
            static::query()
                ->where('registration_number', 'LIKE', "{$prefix}%")
                ->select(
                    DB::raw(
                        "MAX(CAST(SUBSTR(registration_number, LENGTH('$prefix') + 1) AS UNSIGNED)) as max",
                    ),
                )
                ->value('max') ?? 0;

        // Generate next counter (4-digit padded)
        $nextCounter = str_pad(
            (string) ($lastNumber + 1),
            4,
            '0',
            STR_PAD_LEFT,
        );

        return $prefix.$nextCounter;
    }

    public function progression(): HasMany
    {
        return $this->hasMany(StudentProgression::class, 'student_id');
    }

    public function exams(): HasMany
    {
        return $this->hasMany(ExamStudent::class, 'student_id');
    }
}
